"use server";

import { z } from "zod";
import { SaleSchema } from "@/schemas/zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import {
  createSaleSuccessNotification,
  createLowStockNotification,
} from "@/lib/create-system-notification";
import { generateSalesId } from "@/lib/generate-id";
import { canCreateTransaction } from "@/lib/subscription-limits";
import { getUserSubscription } from "@/lib/subscription";

// Function to get the next transaction number with global uniqueness and atomic generation
export const getNextTransactionNumber = async (
  prefix: string,
  date?: Date | string
) => {
  try {
    // Use provided date or current date
    const baseDate = date ? new Date(date) : new Date();
    const year = baseDate.getFullYear();
    const yearSuffix = String(year).slice(-2); // Get last 2 digits of year

    // Determine the new prefix and search pattern based on the input prefix
    let newPrefix: string;
    let regexPattern: RegExp;

    if (prefix.toUpperCase() === "TRX") {
      // For sales transactions: JUAL-{YY}J{NNNNNN}
      newPrefix = "JUAL";
      // Regex to match only auto-generated sequential numbers: JUAL-25J000001, JUAL-25J000002, etc.
      regexPattern = new RegExp(`^JUAL-${yearSuffix}J\\d{6}$`);
    } else {
      // For other prefixes (like INV), keep the old behavior for now
      newPrefix = prefix.toUpperCase();
      regexPattern = new RegExp(`^${prefix.toUpperCase()}-${year}-S\\d{6}$`);
    }

    // Use atomic transaction to prevent race conditions
    const result = await db.$transaction(async (tx) => {
      // Count GLOBALLY across all users for auto-generated transaction numbers only
      // This ensures global uniqueness and excludes custom/manual entries
      if (prefix.toUpperCase() === "TRX") {
        // Get all sales with transaction numbers that start with the pattern
        const salesWithAutoTrx = await tx.sale.findMany({
          where: {
            transactionNumber: {
              startsWith: `JUAL-${yearSuffix}J`,
            },
          },
          select: {
            transactionNumber: true,
          },
          orderBy: {
            transactionNumber: "desc",
          },
        });

        // Filter to only auto-generated sequential numbers and find the highest
        let maxNumber = 0;
        for (const sale of salesWithAutoTrx) {
          if (
            sale.transactionNumber &&
            regexPattern.test(sale.transactionNumber)
          ) {
            const match = sale.transactionNumber.match(
              new RegExp(`^JUAL-${yearSuffix}J(\\d{6})$`)
            );
            if (match && match[1]) {
              const currentNumber = parseInt(match[1], 10);
              if (currentNumber > maxNumber) {
                maxNumber = currentNumber;
              }
            }
          }
        }

        const nextNumber = maxNumber + 1;

        // New format: JUAL-25J000001
        const formattedNumber = String(nextNumber).padStart(6, "0");
        const transactionNumber = `${newPrefix}-${yearSuffix}J${formattedNumber}`;

        // Double-check that this number doesn't exist (extra safety)
        const existingTrx = await tx.sale.findFirst({
          where: {
            transactionNumber: transactionNumber,
          },
          select: { id: true },
        });

        if (existingTrx) {
          // If somehow it exists, try the next number
          const nextFormattedNumber = String(nextNumber + 1).padStart(6, "0");
          return `${newPrefix}-${yearSuffix}J${nextFormattedNumber}`;
        }

        return transactionNumber;
      } else {
        // Keep old format for other prefixes (like INV)
        const salesWithOldTrx = await tx.sale.findMany({
          where: {
            transactionNumber: {
              startsWith: `${prefix.toUpperCase()}-${year}-`,
            },
          },
          select: {
            transactionNumber: true,
          },
          orderBy: {
            transactionNumber: "desc",
          },
        });

        // Filter to only auto-generated sequential numbers and find the highest
        let maxNumber = 0;
        for (const sale of salesWithOldTrx) {
          if (
            sale.transactionNumber &&
            regexPattern.test(sale.transactionNumber)
          ) {
            const match = sale.transactionNumber.match(
              new RegExp(`^${prefix.toUpperCase()}-${year}-S(\\d{6})$`)
            );
            if (match && match[1]) {
              const currentNumber = parseInt(match[1], 10);
              if (currentNumber > maxNumber) {
                maxNumber = currentNumber;
              }
            }
          }
        }

        const nextNumber = maxNumber + 1;
        const formattedNumber = `S${String(nextNumber).padStart(6, "0")}`;
        const transactionNumber = `${prefix.toUpperCase()}-${year}-${formattedNumber}`;

        // Double-check that this number doesn't exist
        const existingTrx = await tx.sale.findFirst({
          where: {
            transactionNumber: transactionNumber,
          },
          select: { id: true },
        });

        if (existingTrx) {
          const nextFormattedNumber = `S${String(nextNumber + 1).padStart(6, "0")}`;
          return `${prefix.toUpperCase()}-${year}-${nextFormattedNumber}`;
        }

        return transactionNumber;
      }
    });

    return {
      success: true,
      nextNumber: result,
    };
  } catch (error) {
    console.error("Error generating next transaction number:", error);

    // Get current year for fallback
    const fallbackDate = date ? new Date(date) : new Date();
    const year = fallbackDate.getFullYear();
    const yearSuffix = String(year).slice(-2);

    // Fallback based on prefix
    if (prefix.toUpperCase() === "TRX") {
      return {
        success: true,
        nextNumber: `JUAL-${yearSuffix}J000001`,
      };
    } else {
      return {
        success: true,
        nextNumber: `${prefix.toUpperCase()}-${year}-S000001`,
      };
    }
  }
};

// Function to get the next invoice number with global uniqueness across sales and purchases
export const getNextInvoiceNumber = async (date?: Date | string) => {
  try {
    // Use provided date or current date
    const baseDate = date ? new Date(date) : new Date();
    const year = baseDate.getFullYear();
    const yearSuffix = String(year).slice(-2); // Get last 2 digits of year

    // Regex to match only auto-generated sequential invoice numbers: INV-25J000001, INV-25B000001, etc.
    const invoiceRegexPattern = new RegExp(`^INV-${yearSuffix}[JB]\\d{6}$`);

    // Count GLOBALLY across both sales and purchases for auto-generated invoice numbers only
    // Get all sales invoice references that match the auto-generated pattern
    const salesWithAutoInv = await db.sale.findMany({
      where: {
        invoiceRef: {
          startsWith: `INV-${yearSuffix}`,
        },
      },
      select: {
        invoiceRef: true,
      },
    });

    // Get all purchase invoice references that match the auto-generated pattern
    const purchasesWithAutoInv = await db.purchase.findMany({
      where: {
        invoiceRef: {
          startsWith: `INV-${yearSuffix}`,
        },
      },
      select: {
        invoiceRef: true,
      },
    });

    // Combine and filter to only count auto-generated sequential numbers
    const allAutoInvoices = [
      ...salesWithAutoInv.map((s) => s.invoiceRef),
      ...purchasesWithAutoInv.map((p) => p.invoiceRef),
    ].filter(
      (invoiceRef) => invoiceRef && invoiceRegexPattern.test(invoiceRef)
    );

    // Calculate the next number (add 1 to the current count)
    const nextNumber = allAutoInvoices.length + 1;

    // Format: INV-25J000001 (J for sales)
    const formattedNumber = String(nextNumber).padStart(6, "0");
    const result = `INV-${yearSuffix}J${formattedNumber}`;

    return {
      success: true,
      nextNumber: result,
    };
  } catch (error) {
    console.error("Error generating next invoice number:", error);

    // Get current year for fallback
    const fallbackDate = date ? new Date(date) : new Date();
    const year = fallbackDate.getFullYear();
    const yearSuffix = String(year).slice(-2);

    return {
      success: true,
      nextNumber: `INV-${yearSuffix}J000001`,
    };
  }
};

export const addSale = async (values: z.infer<typeof SaleSchema>) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Check subscription limits before creating sale
  try {
    const userSubscription = await getUserSubscription(userId);
    const limitCheck = await canCreateTransaction(
      userId,
      userSubscription.plan
    );

    if (!limitCheck.allowed) {
      return {
        error:
          limitCheck.message ||
          "Batas transaksi bulanan tercapai untuk paket Anda.",
      };
    }
  } catch (error) {
    console.error("Error checking subscription limits:", error);
    return { error: "Gagal memeriksa batas langganan." };
  }

  // 2. Validate input server-side
  const validatedFields = SaleSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    items,
    totalAmount,
    transactionNumber: inputTransactionNumber,
    invoiceRef: inputInvoiceRef,
    isDraft,
    customerId,
    customerRefNumber,
    shippingAddress,
    paymentDueDate,
    paymentTerms,
    warehouse,
    tags,
    memo,
    lampiran,
    priceIncludesTax,
  } = validatedFields.data;

  try {
    // 2. Create sale in database with transaction to ensure all operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
      // Generate a custom sales ID with company-specific sequence
      const customSalesId = await generateSalesId(userId, tx);

      // Generate transaction number if not provided
      let transactionNumber = inputTransactionNumber;
      if (!transactionNumber || transactionNumber.trim() === "") {
        const trxResult = await getNextTransactionNumber("TRX");
        if (trxResult.success) {
          transactionNumber = trxResult.nextNumber;
        }
      }

      // Generate invoice number if not provided
      let invoiceRef = inputInvoiceRef;
      if (!invoiceRef || invoiceRef.trim() === "") {
        const invResult = await getNextInvoiceNumber();
        if (invResult.success) {
          invoiceRef = invResult.nextNumber;
        }
      }

      // Create the sale record with custom ID
      const sale = await tx.sale.create({
        data: {
          id: customSalesId, // Use the custom ID
          totalAmount,
          userId,
          transactionNumber, // Set the transaction number
          invoiceRef, // Set the invoice reference
          isDraft: isDraft || false,
          // Customer relationship and additional fields
          customerId: customerId || null,
          customerRefNumber: customerRefNumber || null,
          shippingAddress: shippingAddress || null,
          paymentDueDate: paymentDueDate || null,
          paymentTerms: paymentTerms || null,
          warehouseId: warehouse || null,
          tags: tags || [],
          memo: memo || null,
          lampiran: lampiran || [],
          priceIncludesTax: priceIncludesTax || false,
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              priceAtSale: item.priceAtSale,
              productId: item.productId,
              // Include new discount and metadata fields
              discountPercentage: item.discountPercentage || null,
              discountAmount: item.discountAmount || null,
              eventDiscountId: item.eventDiscountId || null,
              eventDiscountName: item.eventDiscountName || null,
              isWholesale: item.isWholesale || false,
              unit: item.unit || "Buah",
              tax: item.tax || null,
            })),
          },
        },
        include: {
          items: true,
        },
      });

      // Update product stock for each item sold and check for low stock (only if not a draft)
      const lowStockThreshold = 5; // Define a threshold for low stock notifications
      const lowStockProducts = [];

      if (!isDraft) {
        for (const item of items) {
          // Get the product to check current stock
          const product = await tx.product.findUnique({
            where: { id: item.productId },
            select: { id: true, name: true, stock: true },
          });

          if (!product) continue;

          // Calculate new stock level
          const newStock = product.stock - item.quantity;

          // Update the stock
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity,
              },
            },
          });

          // Check if stock is below threshold
          if (newStock <= lowStockThreshold && newStock > 0) {
            lowStockProducts.push({
              id: product.id,
              name: product.name,
              stock: newStock,
            });
          }
        }
      }

      return sale;
    });

    // 3. Create notifications
    // Sale success notification
    await createSaleSuccessNotification(
      result.id,
      result.totalAmount.toNumber()
    );

    // Low stock notifications
    const lowStockProducts = [];

    // Get updated stock levels for products in this sale
    for (const item of items) {
      const product = await db.product.findUnique({
        where: { id: item.productId },
        select: { id: true, name: true, stock: true },
      });

      if (product && product.stock <= 5 && product.stock > 0) {
        lowStockProducts.push(product);
      }
    }

    // Create low stock notifications
    for (const product of lowStockProducts) {
      await createLowStockNotification(product.name, product.stock);
    }

    // 4. Revalidate the sales page cache
    revalidatePath("/dashboard/sales");

    // Convert Decimal to number for serialization
    const serializedResult = {
      ...result,
      totalAmount: result.totalAmount.toNumber(),
      items: result.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
      })),
    };

    return {
      success: "Penjualan berhasil dicatat!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mencatat penjualan. Silakan coba lagi." };
  }
};

export const getSales = async () => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    const sales = await db.sale.findMany({
      where: {
        userId,
      },
      orderBy: {
        saleDate: "desc",
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            NIK: true,
            NPWP: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            username: true,
            additionalInfo: {
              select: {
                companyName: true,
                companyAddress: true,
                companyPhone: true,
                companyEmail: true,
              },
            },
          },
        },
        Employee: {
          select: {
            id: true,
            name: true,
            employeeId: true,
            role: true,
          },
        },
      },
    });

    // Convert Decimal to number for serialization
    const serializedSales = sales.map((sale) => ({
      ...sale,
      totalAmount: sale.totalAmount.toNumber(),
      items: sale.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
        // Convert Decimal discount fields to numbers
        discountPercentage: item.discountPercentage
          ? item.discountPercentage.toNumber()
          : null,
        discountAmount: item.discountAmount
          ? item.discountAmount.toNumber()
          : null,
      })),
    }));

    return { sales: serializedSales };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil data penjualan." };
  }
};

export const getSaleById = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // First try to find by transaction number (case-insensitive)
    let sale = await db.sale.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the sale belongs to the current user
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            NIK: true,
            NPWP: true,
          },
        },
        warehouse: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            username: true,
            additionalInfo: {
              select: {
                companyName: true,
                companyAddress: true,
                companyPhone: true,
                companyEmail: true,
              },
            },
          },
        },
        Employee: {
          select: {
            id: true,
            name: true,
            employeeId: true,
            role: true,
          },
        },
      },
    });

    // If not found by transaction number, check if the ID starts with "sal-" (case-insensitive)
    if (!sale && id.toLowerCase().startsWith("sal-")) {
      sale = await db.sale.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              NIK: true,
              NPWP: true,
            },
          },
          warehouse: {
            select: {
              id: true,
              name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              username: true,
              additionalInfo: {
                select: {
                  companyName: true,
                  companyAddress: true,
                  companyPhone: true,
                  companyEmail: true,
                },
              },
            },
          },
          Employee: {
            select: {
              id: true,
              name: true,
              employeeId: true,
              role: true,
            },
          },
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!sale) {
      sale = await db.sale.findUnique({
        where: {
          id,
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              NIK: true,
              NPWP: true,
            },
          },
          warehouse: {
            select: {
              id: true,
              name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              username: true,
              additionalInfo: {
                select: {
                  companyName: true,
                  companyAddress: true,
                  companyPhone: true,
                  companyEmail: true,
                },
              },
            },
          },
          Employee: {
            select: {
              id: true,
              name: true,
              employeeId: true,
              role: true,
            },
          },
        },
      });
    }

    if (!sale) {
      return { error: "Penjualan tidak ditemukan." };
    }

    // Convert Decimal to number for serialization
    const serializedSale = {
      ...sale,
      totalAmount: sale.totalAmount.toNumber(),
      items: sale.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
        // Convert discount fields to numbers
        discountPercentage: item.discountPercentage
          ? item.discountPercentage.toNumber()
          : null,
        discountAmount: item.discountAmount
          ? item.discountAmount.toNumber()
          : null,
        product: {
          ...item.product,
          price: item.product.price.toNumber(),
          cost: item.product.cost ? item.product.cost.toNumber() : null,
          // Convert all other Decimal fields to numbers
          wholesalePrice: item.product.wholesalePrice
            ? item.product.wholesalePrice.toNumber()
            : null,
          weight: item.product.weight, // weight is Int, not Decimal
          length: item.product.length ? item.product.length.toNumber() : null,
          width: item.product.width ? item.product.width.toNumber() : null,
          height: item.product.height ? item.product.height.toNumber() : null,
          // Convert tax rate Decimal fields to numbers
          salePriceTaxRate: item.product.salePriceTaxRate
            ? item.product.salePriceTaxRate.toNumber()
            : null,
          wholesalePriceTaxRate: item.product.wholesalePriceTaxRate
            ? item.product.wholesalePriceTaxRate.toNumber()
            : null,
          costPriceTaxRate: item.product.costPriceTaxRate
            ? item.product.costPriceTaxRate.toNumber()
            : null,
        },
      })),
    };

    return { sale: serializedSale };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil detail penjualan." };
  }
};

export const updateSale = async (
  id: string,
  values: z.infer<typeof SaleSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = SaleSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    items,
    totalAmount,
    invoiceRef,
    transactionNumber,
    isDraft,
    customerId,
    customerRefNumber,
    shippingAddress,
    paymentDueDate,
    paymentTerms,
    warehouse,
    tags,
    memo,
    lampiran,
    priceIncludesTax,
  } = validatedFields.data;

  try {
    // First try to find by transaction number (case-insensitive)
    let existingSale = await db.sale.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the sale belongs to the current user
      },
      include: {
        items: true,
      },
    });

    // If not found by transaction number, check if the ID starts with "sal-" (case-insensitive)
    if (!existingSale && id.toLowerCase().startsWith("sal-")) {
      existingSale = await db.sale.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: true,
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!existingSale) {
      existingSale = await db.sale.findUnique({
        where: {
          id,
          userId,
        },
        include: {
          items: true,
        },
      });
    }

    if (!existingSale) {
      return { error: "Penjualan tidak ditemukan!" };
    }

    // Get the original items to calculate stock adjustments
    const originalItems = existingSale.items;

    // 2. Update sale in database with transaction to ensure all operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
      // First, delete all existing items
      await tx.saleItem.deleteMany({
        where: {
          saleId: id,
        },
      });

      // Update the sale record
      const sale = await tx.sale.update({
        where: {
          id,
        },
        data: {
          totalAmount,
          transactionNumber,
          invoiceRef,
          isDraft: isDraft || false,
          // Customer relationship and additional fields
          customerId: customerId || null,
          customerRefNumber: customerRefNumber || null,
          shippingAddress: shippingAddress || null,
          paymentDueDate: paymentDueDate || null,
          paymentTerms: paymentTerms || null,
          warehouseId: warehouse || null,
          tags: tags || [],
          memo: memo || null,
          lampiran: lampiran || [],
          priceIncludesTax: priceIncludesTax || false,
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              priceAtSale: item.priceAtSale,
              productId: item.productId,
              // Include new discount and metadata fields
              discountPercentage: item.discountPercentage || null,
              discountAmount: item.discountAmount || null,
              eventDiscountId: item.eventDiscountId || null,
              eventDiscountName: item.eventDiscountName || null,
              isWholesale: item.isWholesale || false,
              unit: item.unit || "Buah",
              tax: item.tax || null,
            })),
          },
        },
        include: {
          items: true,
        },
      });

      // Adjust product stock: first add back the original quantities, then subtract the new quantities
      // This handles both new items, removed items, and quantity changes
      // Only adjust stock if the sale is not a draft

      // Add back original quantities (reverse the original sale) - only if original was not a draft
      if (!existingSale.isDraft) {
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          });
        }
      }

      // Subtract new quantities (apply the updated sale) - only if new sale is not a draft
      if (!isDraft) {
        for (const item of items) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity,
              },
            },
          });
        }
      }

      return sale;
    });

    // 3. Revalidate the sales page cache
    revalidatePath("/dashboard/sales");
    revalidatePath(`/dashboard/sales/detail/${id}`);
    revalidatePath(`/dashboard/sales/edit/${id}`);

    // Also revalidate using transactionNumber if available
    if (existingSale.transactionNumber) {
      revalidatePath(
        `/dashboard/sales/detail/${existingSale.transactionNumber}`
      );
      revalidatePath(`/dashboard/sales/edit/${existingSale.transactionNumber}`);
    }

    // Convert Decimal to number for serialization
    const serializedResult = {
      ...result,
      totalAmount: result.totalAmount.toNumber(),
      items: result.items.map((item) => ({
        ...item,
        priceAtSale: item.priceAtSale.toNumber(),
      })),
    };

    return {
      success: "Penjualan berhasil diperbarui!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui penjualan. Silakan coba lagi." };
  }
};

export const deleteSale = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // First try to find by transaction number (case-insensitive)
    let existingSale = await db.sale.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the sale belongs to the current user
      },
      include: {
        items: true,
      },
    });

    // If not found by transaction number, check if the ID starts with "sal-" (case-insensitive)
    if (!existingSale && id.toLowerCase().startsWith("sal-")) {
      existingSale = await db.sale.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the sale belongs to the current user
        },
        include: {
          items: true,
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!existingSale) {
      existingSale = await db.sale.findUnique({
        where: {
          id,
          userId,
        },
        include: {
          items: true,
        },
      });
    }

    if (!existingSale) {
      return { error: "Penjualan tidak ditemukan!" };
    }

    // Get the original items to revert stock changes
    const originalItems = existingSale.items;

    // Use a transaction to ensure all operations succeed or fail together
    await db.$transaction(async (tx) => {
      // First, revert the stock changes by incrementing the stock for each item (only if not a draft)
      if (!existingSale.isDraft) {
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          });
        }
      }

      // Delete all sale items
      await tx.saleItem.deleteMany({
        where: {
          saleId: id,
        },
      });

      // Delete the sale
      await tx.sale.delete({
        where: {
          id,
          userId, // Ensure the sale belongs to the current user
        },
      });
    });

    // Revalidate the sales page cache
    revalidatePath("/dashboard/sales");
    revalidatePath(`/dashboard/sales/detail/${id}`);
    revalidatePath(`/dashboard/sales/edit/${id}`);

    // Also revalidate using transactionNumber if available
    if (existingSale.transactionNumber) {
      revalidatePath(
        `/dashboard/sales/detail/${existingSale.transactionNumber}`
      );
      revalidatePath(`/dashboard/sales/edit/${existingSale.transactionNumber}`);
    }

    return { success: "Penjualan berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus penjualan. Silakan coba lagi." };
  }
};
